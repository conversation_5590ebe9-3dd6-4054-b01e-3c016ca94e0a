import { renderHook, act } from '@testing-library/react';
import { useMultiplayer } from '../../hooks/useMultiplayer.js';
import { GameMode, MessageType } from '@shared/schema.js';

// Mock WebSocket
const mockWebSocket = {
  send: jest.fn(),
  close: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  readyState: 1, // WebSocket.OPEN
};

// Mock global WebSocket constructor with proper constants
const MockWebSocketConstructor = jest.fn().mockImplementation(() => mockWebSocket);
(MockWebSocketConstructor as any).CONNECTING = 0;
(MockWebSocketConstructor as any).OPEN = 1;
(MockWebSocketConstructor as any).CLOSING = 2;
(MockWebSocketConstructor as any).CLOSED = 3;
global.WebSocket = MockWebSocketConstructor as any;

describe('useMultiplayer', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should initialize with default values', () => {
    const { result } = renderHook(() => useMultiplayer({
      url: 'ws://localhost:5001',
      autoConnect: false
    }));

    expect(result.current.connected).toBe(false);
    expect(result.current.connectionId).toBe(null);
    expect(result.current.players).toEqual([]);
    expect(result.current.gameState).toBe(null);
  });

  test('should connect to WebSocket server', () => {
    const { result } = renderHook(() => useMultiplayer({
      url: 'ws://localhost:5001',
      autoConnect: false
    }));

    // Simulate connection
    act(() => {
      result.current.connect();
    });

    // Should create WebSocket
    expect(WebSocket).toHaveBeenCalled();

    // Should add event listeners
    expect(mockWebSocket.addEventListener).toHaveBeenCalledWith('open', expect.any(Function));
    expect(mockWebSocket.addEventListener).toHaveBeenCalledWith('message', expect.any(Function));
    expect(mockWebSocket.addEventListener).toHaveBeenCalledWith('close', expect.any(Function));
    expect(mockWebSocket.addEventListener).toHaveBeenCalledWith('error', expect.any(Function));
  });

  test('should join an arena', () => {
    const { result } = renderHook(() => useMultiplayer({
      url: 'ws://localhost:5001',
      autoConnect: false
    }));

    // Simulate connection
    act(() => {
      result.current.connect();
    });

    // Simulate open event
    const openHandler = mockWebSocket.addEventListener.mock.calls.find((call: any) => call[0] === 'open')[1];
    act(() => {
      openHandler();
    });

    // Join an arena
    act(() => {
      result.current.joinArena(1, 'Player1');
    });

    // Should send join arena message
    expect(mockWebSocket.send).toHaveBeenCalled();
  });

  test('should start a game', () => {
    const { result } = renderHook(() => useMultiplayer({
      url: 'ws://localhost:5001',
      autoConnect: false
    }));

    // Simulate connection
    act(() => {
      result.current.connect();
    });

    // Simulate open event
    const openHandler = mockWebSocket.addEventListener.mock.calls.find((call: any) => call[0] === 'open')[1];
    act(() => {
      openHandler();
    });

    // Start a game
    act(() => {
      result.current.startGame();
    });

    // Should send start game message
    expect(mockWebSocket.send).toHaveBeenCalledWith(
      expect.stringContaining(MessageType.GameStart)
    );
  });

  test('should handle player state updates', () => {
    const { result } = renderHook(() => useMultiplayer({
      url: 'ws://localhost:5001',
      autoConnect: false
    }));

    // Simulate connection
    act(() => {
      result.current.connect();
    });

    // Simulate open event
    const openHandler = mockWebSocket.addEventListener.mock.calls.find((call: any) => call[0] === 'open')[1];
    act(() => {
      openHandler();
    });

    // Update player state
    act(() => {
      result.current.updatePlayerState({
        position: { x: 10, y: 5, z: 20 },
        health: 100,
        jetpackFuel: 100
      });
    });

    // Should send player update message
    expect(mockWebSocket.send).toHaveBeenCalledWith(
      expect.stringContaining(MessageType.PlayerUpdate)
    );
  });

  test('should handle weapon effect creation', () => {
    const { result } = renderHook(() => useMultiplayer({
      url: 'ws://localhost:5001',
      autoConnect: false
    }));

    // Simulate connection
    act(() => {
      result.current.connect();
    });

    // Simulate open event
    const openHandler = mockWebSocket.addEventListener.mock.calls.find((call: any) => call[0] === 'open')[1];
    act(() => {
      openHandler();
    });

    // Create weapon effect
    const effectData = {
      id: 'effect-123',
      type: 'gravity' as const,
      position: { x: 10, y: 5, z: 20 },
      radius: 5,
      timeLeft: 3
    };

    act(() => {
      result.current.createWeaponEffect(effectData);
    });

    // Should send weapon effect message
    expect(mockWebSocket.send).toHaveBeenCalledWith(
      expect.stringContaining(MessageType.WeaponEffectCreated)
    );
  });

  test('should handle incoming game start messages', () => {
    const { result } = renderHook(() => useMultiplayer({
      url: 'ws://localhost:5001',
      autoConnect: false
    }));

    // Simulate connection
    act(() => {
      result.current.connect();
    });

    // Get message handler
    const messageHandler = mockWebSocket.addEventListener.mock.calls.find((call: any) => call[0] === 'message')[1];

    // Simulate game start message
    const gameStartMessage = {
      type: MessageType.GameStart,
      data: {
        id: 123,
        mode: GameMode.CoOp,
        status: 'in_progress',
        currentLevel: 2,
        enemiesTotal: 10,
        enemiesDefeated: 5,
        players: [
          { id: 'player-1', name: 'Player1', position: { x: 0, y: 0, z: 0 } }
        ]
      }
    };

    act(() => {
      messageHandler({ data: JSON.stringify(gameStartMessage) });
    });

    // Game state should be updated
    expect(result.current.gameState).toEqual(gameStartMessage.data);
  });

  test('should disconnect from WebSocket server', () => {
    const { result } = renderHook(() => useMultiplayer({
      url: 'ws://localhost:5001',
      autoConnect: false
    }));

    // Simulate connection
    act(() => {
      result.current.connect();
    });

    // Disconnect
    act(() => {
      result.current.disconnect();
    });

    // Should close WebSocket
    expect(mockWebSocket.close).toHaveBeenCalled();
  });
});
