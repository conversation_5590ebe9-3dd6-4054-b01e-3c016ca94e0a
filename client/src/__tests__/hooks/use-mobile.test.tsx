import { renderHook } from '@testing-library/react';
import { useIsMobile } from '../../hooks/use-mobile.jsx';

describe('useIsMobile', () => {
  const originalMatchMedia = window.matchMedia;
  
  afterEach(() => {
    window.matchMedia = originalMatchMedia;
  });
  
  test('should return true for mobile devices', () => {
    // Mock matchMedia to simulate mobile device
    window.matchMedia = jest.fn().mockImplementation(query => ({
      matches: query.includes('max-width: 768px'),
      media: query,
      onchange: null,
      addListener: jest.fn(),
      removeListener: jest.fn(),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    }));
    
    const { result } = renderHook(() => useIsMobile());
    
    expect(result.current).toBe(true);
  });
  
  test('should return false for desktop devices', () => {
    // Mock matchMedia to simulate desktop device
    window.matchMedia = jest.fn().mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: jest.fn(),
      removeListener: jest.fn(),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    }));
    
    const { result } = renderHook(() => useIsMobile());
    
    expect(result.current).toBe(false);
  });
});
