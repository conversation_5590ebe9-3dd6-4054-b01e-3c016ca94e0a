npx tsc --noEmit --project .
client/src/__tests__/game/controls/MobileControls.test.tsx:8:1 - error TS2304: Cannot find name 'jest'.

8 jest.mock('nipplejs', () => ({
  ~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:9:11 - error TS2304: Cannot find name 'jest'.

9   create: jest.fn().mockReturnValue({
            ~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:10:9 - error TS2304: Cannot find name 'jest'.

10     on: jest.fn(),
           ~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:11:14 - error TS2304: Cannot find name 'jest'.

11     destroy: jest.fn(),
                ~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:15:1 - error TS2593: Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha` and then add 'jest' or 'mocha' to the types field in your tsconfig.

15 describe('MobileControls', () => {
   ~~~~~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:17:22 - error TS2304: Cannot find name 'jest'.

17   const mockOnMove = jest.fn();
                        ~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:18:22 - error TS2304: Cannot find name 'jest'.

18   const mockOnLook = jest.fn();
                        ~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:19:22 - error TS2304: Cannot find name 'jest'.

19   const mockOnJump = jest.fn();
                        ~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:20:23 - error TS2304: Cannot find name 'jest'.

20   const mockOnShoot = jest.fn();
                         ~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:21:30 - error TS2304: Cannot find name 'jest'.

21   const mockOnSwitchWeapon = jest.fn();
                                ~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:22:25 - error TS2304: Cannot find name 'jest'.

22   const mockOnGrapple = jest.fn();
                           ~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:23:25 - error TS2304: Cannot find name 'jest'.

23   const mockOnJetpack = jest.fn();
                           ~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:24:26 - error TS2304: Cannot find name 'jest'.

24   const mockOnInteract = jest.fn();
                            ~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:26:3 - error TS2304: Cannot find name 'beforeEach'.

26   beforeEach(() => {
     ~~~~~~~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:28:5 - error TS2304: Cannot find name 'jest'.

28     jest.clearAllMocks();
       ~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:31:3 - error TS2593: Cannot find name 'test'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha` and then add 'jest' or 'mocha' to the types field in your tsconfig.

31   test('should render correctly', () => {
     ~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:46:5 - error TS2304: Cannot find name 'expect'.

46     expect(document.querySelector('.left-joystick')).toBeInTheDocument();
       ~~~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:47:5 - error TS2304: Cannot find name 'expect'.

47     expect(document.querySelector('.right-joystick')).toBeInTheDocument();
       ~~~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:50:5 - error TS2304: Cannot find name 'expect'.

50     expect(screen.getByText('JUMP')).toBeInTheDocument();
       ~~~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:51:5 - error TS2304: Cannot find name 'expect'.

51     expect(screen.getByText('SHOOT')).toBeInTheDocument();
       ~~~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:52:5 - error TS2304: Cannot find name 'expect'.

52     expect(screen.getByText('GRAPPLE')).toBeInTheDocument();
       ~~~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:55:3 - error TS2593: Cannot find name 'test'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha` and then add 'jest' or 'mocha' to the types field in your tsconfig.

55   test('should initialize joysticks', () => {
     ~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:70:5 - error TS2304: Cannot find name 'expect'.

70     expect(nipplejs.create).toHaveBeenCalledTimes(2);
       ~~~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:73:5 - error TS2304: Cannot find name 'expect'.

73     expect(nipplejs.create).toHaveBeenCalledWith(
       ~~~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:74:7 - error TS2304: Cannot find name 'expect'.

74       expect.objectContaining({
         ~~~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:76:19 - error TS2304: Cannot find name 'expect'.

76         position: expect.objectContaining({ left: '100px', bottom: '100px' }),
                     ~~~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:81:5 - error TS2304: Cannot find name 'expect'.

81     expect(nipplejs.create).toHaveBeenCalledWith(
       ~~~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:82:7 - error TS2304: Cannot find name 'expect'.

82       expect.objectContaining({
         ~~~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:84:19 - error TS2304: Cannot find name 'expect'.

84         position: expect.objectContaining({ right: '100px', bottom: '100px' }),
                     ~~~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:89:3 - error TS2593: Cannot find name 'test'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha` and then add 'jest' or 'mocha' to the types field in your tsconfig.

89   test('should call onJump when jump button is clicked', () => {
     ~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:107:5 - error TS2304: Cannot find name 'expect'.

107     expect(mockOnJump).toHaveBeenCalledTimes(1);
        ~~~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:110:3 - error TS2593: Cannot find name 'test'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha` and then add 'jest' or 'mocha' to the types field in your tsconfig.

110   test('should call onShoot when shoot button is touched', () => {
      ~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:128:5 - error TS2304: Cannot find name 'expect'.

128     expect(mockOnShoot).toHaveBeenCalledTimes(1);
        ~~~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:131:3 - error TS2593: Cannot find name 'test'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha` and then add 'jest' or 'mocha' to the types field in your tsconfig.

131   test('should call onGrapple when grapple button is clicked', () => {
      ~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:149:5 - error TS2304: Cannot find name 'expect'.

149     expect(mockOnGrapple).toHaveBeenCalledTimes(1);
        ~~~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:152:3 - error TS2593: Cannot find name 'test'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha` and then add 'jest' or 'mocha' to the types field in your tsconfig.

152   test('should toggle jetpack state when jetpack button is clicked', () => {
      ~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:173:5 - error TS2304: Cannot find name 'expect'.

173     expect(mockOnJetpack).toHaveBeenCalledWith(true);
        ~~~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:179:5 - error TS2304: Cannot find name 'expect'.

179     expect(mockOnJetpack).toHaveBeenCalledWith(false);
        ~~~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:182:3 - error TS2593: Cannot find name 'test'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha` and then add 'jest' or 'mocha' to the types field in your tsconfig.

182   test('should call onSwitchWeapon when weapon buttons are clicked', () => {
      ~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:205:5 - error TS2304: Cannot find name 'expect'.

205     expect(mockOnSwitchWeapon).toHaveBeenCalledWith(0);
        ~~~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:211:5 - error TS2304: Cannot find name 'expect'.

211     expect(mockOnSwitchWeapon).toHaveBeenCalledWith(1);
        ~~~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:217:5 - error TS2304: Cannot find name 'expect'.

217     expect(mockOnSwitchWeapon).toHaveBeenCalledWith(2);
        ~~~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:220:3 - error TS2593: Cannot find name 'test'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha` and then add 'jest' or 'mocha' to the types field in your tsconfig.

220   test('should handle joystick movement correctly', () => {
      ~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:235:32 - error TS2554: Expected 1 arguments, but got 0.

235     const onMethod = (nipplejs.create() as any).on;
                                   ~~~~~~

  node_modules/nipplejs/types/index.d.ts:381:21
    381     function create(options: JoystickManagerOptions): JoystickManager;
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    An argument for 'options' was not provided.

client/src/__tests__/game/controls/MobileControls.test.tsx:238:51 - error TS7006: Parameter 'call' implicitly has an 'any' type.

238     const moveCallback = onMethod.mock.calls.find(call => call[0] === 'move')[1];
                                                      ~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:248:5 - error TS2304: Cannot find name 'expect'.

248     expect(mockOnMove).toHaveBeenCalledWith(
        ~~~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:249:7 - error TS2304: Cannot find name 'expect'.

249       expect.objectContaining({
          ~~~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:250:12 - error TS2304: Cannot find name 'expect'.

250         x: expect.any(Number),
               ~~~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:252:12 - error TS2304: Cannot find name 'expect'.

252         z: expect.any(Number)
               ~~~~~~

client/src/__tests__/game/controls/MobileControls.test.tsx:258:5 - error TS2304: Cannot find name 'expect'.

258     expect(Math.abs(moveArg.x)).toBeCloseTo(Math.abs(moveArg.z));
        ~~~~~~

client/src/__tests__/hooks/use-mobile.test.tsx:4:1 - error TS2593: Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha` and then add 'jest' or 'mocha' to the types field in your tsconfig.

4 describe('useIsMobile', () => {
  ~~~~~~~~

client/src/__tests__/hooks/use-mobile.test.tsx:7:3 - error TS2304: Cannot find name 'afterEach'.

7   afterEach(() => {
    ~~~~~~~~~

client/src/__tests__/hooks/use-mobile.test.tsx:11:3 - error TS2593: Cannot find name 'test'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha` and then add 'jest' or 'mocha' to the types field in your tsconfig.

11   test('should return true for mobile devices', () => {
     ~~~~

client/src/__tests__/hooks/use-mobile.test.tsx:13:25 - error TS2304: Cannot find name 'jest'.

13     window.matchMedia = jest.fn().mockImplementation(query => ({
                           ~~~~

client/src/__tests__/hooks/use-mobile.test.tsx:13:54 - error TS7006: Parameter 'query' implicitly has an 'any' type.

13     window.matchMedia = jest.fn().mockImplementation(query => ({
                                                        ~~~~~

client/src/__tests__/hooks/use-mobile.test.tsx:17:20 - error TS2304: Cannot find name 'jest'.

17       addListener: jest.fn(),
                      ~~~~

client/src/__tests__/hooks/use-mobile.test.tsx:18:23 - error TS2304: Cannot find name 'jest'.

18       removeListener: jest.fn(),
                         ~~~~

client/src/__tests__/hooks/use-mobile.test.tsx:19:25 - error TS2304: Cannot find name 'jest'.

19       addEventListener: jest.fn(),
                           ~~~~

client/src/__tests__/hooks/use-mobile.test.tsx:20:28 - error TS2304: Cannot find name 'jest'.

20       removeEventListener: jest.fn(),
                              ~~~~

client/src/__tests__/hooks/use-mobile.test.tsx:21:22 - error TS2304: Cannot find name 'jest'.

21       dispatchEvent: jest.fn(),
                        ~~~~

client/src/__tests__/hooks/use-mobile.test.tsx:26:5 - error TS2304: Cannot find name 'expect'.

26     expect(result.current).toBe(true);
       ~~~~~~

client/src/__tests__/hooks/use-mobile.test.tsx:29:3 - error TS2593: Cannot find name 'test'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha` and then add 'jest' or 'mocha' to the types field in your tsconfig.

29   test('should return false for desktop devices', () => {
     ~~~~

client/src/__tests__/hooks/use-mobile.test.tsx:31:25 - error TS2304: Cannot find name 'jest'.

31     window.matchMedia = jest.fn().mockImplementation(query => ({
                           ~~~~

client/src/__tests__/hooks/use-mobile.test.tsx:31:54 - error TS7006: Parameter 'query' implicitly has an 'any' type.

31     window.matchMedia = jest.fn().mockImplementation(query => ({
                                                        ~~~~~

client/src/__tests__/hooks/use-mobile.test.tsx:35:20 - error TS2304: Cannot find name 'jest'.

35       addListener: jest.fn(),
                      ~~~~

client/src/__tests__/hooks/use-mobile.test.tsx:36:23 - error TS2304: Cannot find name 'jest'.

36       removeListener: jest.fn(),
                         ~~~~

client/src/__tests__/hooks/use-mobile.test.tsx:37:25 - error TS2304: Cannot find name 'jest'.

37       addEventListener: jest.fn(),
                           ~~~~

client/src/__tests__/hooks/use-mobile.test.tsx:38:28 - error TS2304: Cannot find name 'jest'.

38       removeEventListener: jest.fn(),
                              ~~~~

client/src/__tests__/hooks/use-mobile.test.tsx:39:22 - error TS2304: Cannot find name 'jest'.

39       dispatchEvent: jest.fn(),
                        ~~~~

client/src/__tests__/hooks/use-mobile.test.tsx:44:5 - error TS2304: Cannot find name 'expect'.

44     expect(result.current).toBe(false);
       ~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:7:9 - error TS2304: Cannot find name 'jest'.

7   send: jest.fn(),
          ~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:8:10 - error TS2304: Cannot find name 'jest'.

8   close: jest.fn(),
           ~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:9:21 - error TS2304: Cannot find name 'jest'.

9   addEventListener: jest.fn(),
                      ~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:10:24 - error TS2304: Cannot find name 'jest'.

10   removeEventListener: jest.fn(),
                          ~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:14:20 - error TS2304: Cannot find name 'jest'.

14 global.WebSocket = jest.fn().mockImplementation(() => mockWebSocket);
                      ~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:16:1 - error TS2593: Cannot find name 'describe'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha` and then add 'jest' or 'mocha' to the types field in your tsconfig.

16 describe('useMultiplayer', () => {
   ~~~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:17:3 - error TS2304: Cannot find name 'beforeEach'.

17   beforeEach(() => {
     ~~~~~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:18:5 - error TS2304: Cannot find name 'jest'.

18     jest.clearAllMocks();
       ~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:21:3 - error TS2593: Cannot find name 'test'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha` and then add 'jest' or 'mocha' to the types field in your tsconfig.

21   test('should initialize with default values', () => {
     ~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:22:41 - error TS2554: Expected 1 arguments, but got 0.

22     const { result } = renderHook(() => useMultiplayer());
                                           ~~~~~~~~~~~~~~

  client/src/hooks/useMultiplayer.ts:51:32
    51 export function useMultiplayer(options: MultiplayerOptions) {
                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~
    An argument for 'options' was not provided.

client/src/__tests__/hooks/useMultiplayer.test.tsx:24:5 - error TS2304: Cannot find name 'expect'.

24     expect(result.current.isConnected).toBe(false);
       ~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:24:27 - error TS2551: Property 'isConnected' does not exist on type '{ connect: () => void; disconnect: () => void; send: (message: NetworkMessage) => boolean; joinArena: (arenaId: number, playerName: string, teamId?: number | undefined) => void; leaveArena: () => void; startGame: () => void; ... 21 more ...; lastErrorCode: number | null; }'. Did you mean 'connected'?

24     expect(result.current.isConnected).toBe(false);
                             ~~~~~~~~~~~

  client/src/hooks/useMultiplayer.ts:37:3
    37   connected: boolean;
         ~~~~~~~~~
    'connected' is declared here.

client/src/__tests__/hooks/useMultiplayer.test.tsx:25:5 - error TS2304: Cannot find name 'expect'.

25     expect(result.current.isHost).toBe(false);
       ~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:25:27 - error TS2339: Property 'isHost' does not exist on type '{ connect: () => void; disconnect: () => void; send: (message: NetworkMessage) => boolean; joinArena: (arenaId: number, playerName: string, teamId?: number | undefined) => void; leaveArena: () => void; startGame: () => void; ... 21 more ...; lastErrorCode: number | null; }'.

25     expect(result.current.isHost).toBe(false);
                             ~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:26:5 - error TS2304: Cannot find name 'expect'.

26     expect(result.current.gameId).toBe(null);
       ~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:26:27 - error TS2339: Property 'gameId' does not exist on type '{ connect: () => void; disconnect: () => void; send: (message: NetworkMessage) => boolean; joinArena: (arenaId: number, playerName: string, teamId?: number | undefined) => void; leaveArena: () => void; startGame: () => void; ... 21 more ...; lastErrorCode: number | null; }'.

26     expect(result.current.gameId).toBe(null);
                             ~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:27:5 - error TS2304: Cannot find name 'expect'.

27     expect(result.current.players).toEqual([]);
       ~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:28:5 - error TS2304: Cannot find name 'expect'.

28     expect(result.current.gameState).toEqual({
       ~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:30:22 - error TS2339: Property 'SINGLE' does not exist on type 'typeof GameMode'.

30       mode: GameMode.SINGLE,
                        ~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:39:3 - error TS2593: Cannot find name 'test'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha` and then add 'jest' or 'mocha' to the types field in your tsconfig.

39   test('should connect to WebSocket server', () => {
     ~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:40:41 - error TS2554: Expected 1 arguments, but got 0.

40     const { result } = renderHook(() => useMultiplayer());
                                           ~~~~~~~~~~~~~~

  client/src/hooks/useMultiplayer.ts:51:32
    51 export function useMultiplayer(options: MultiplayerOptions) {
                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~
    An argument for 'options' was not provided.

client/src/__tests__/hooks/useMultiplayer.test.tsx:44:30 - error TS2554: Expected 0 arguments, but got 1.

44       result.current.connect('Player1');
                                ~~~~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:48:5 - error TS2304: Cannot find name 'expect'.

48     expect(WebSocket).toHaveBeenCalled();
       ~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:51:5 - error TS2304: Cannot find name 'expect'.

51     expect(mockWebSocket.addEventListener).toHaveBeenCalledWith('open', expect.any(Function));
       ~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:51:73 - error TS2304: Cannot find name 'expect'.

51     expect(mockWebSocket.addEventListener).toHaveBeenCalledWith('open', expect.any(Function));
                                                                           ~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:52:5 - error TS2304: Cannot find name 'expect'.

52     expect(mockWebSocket.addEventListener).toHaveBeenCalledWith('message', expect.any(Function));
       ~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:52:76 - error TS2304: Cannot find name 'expect'.

52     expect(mockWebSocket.addEventListener).toHaveBeenCalledWith('message', expect.any(Function));
                                                                              ~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:53:5 - error TS2304: Cannot find name 'expect'.

53     expect(mockWebSocket.addEventListener).toHaveBeenCalledWith('close', expect.any(Function));
       ~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:53:74 - error TS2304: Cannot find name 'expect'.

53     expect(mockWebSocket.addEventListener).toHaveBeenCalledWith('close', expect.any(Function));
                                                                            ~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:54:5 - error TS2304: Cannot find name 'expect'.

54     expect(mockWebSocket.addEventListener).toHaveBeenCalledWith('error', expect.any(Function));
       ~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:54:74 - error TS2304: Cannot find name 'expect'.

54     expect(mockWebSocket.addEventListener).toHaveBeenCalledWith('error', expect.any(Function));
                                                                            ~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:57:3 - error TS2593: Cannot find name 'test'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha` and then add 'jest' or 'mocha' to the types field in your tsconfig.

57   test('should create a new game', () => {
     ~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:58:41 - error TS2554: Expected 1 arguments, but got 0.

58     const { result } = renderHook(() => useMultiplayer());
                                           ~~~~~~~~~~~~~~

  client/src/hooks/useMultiplayer.ts:51:32
    51 export function useMultiplayer(options: MultiplayerOptions) {
                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~
    An argument for 'options' was not provided.

client/src/__tests__/hooks/useMultiplayer.test.tsx:62:30 - error TS2554: Expected 0 arguments, but got 1.

62       result.current.connect('Player1');
                                ~~~~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:66:72 - error TS7006: Parameter 'call' implicitly has an 'any' type.

66     const openHandler = mockWebSocket.addEventListener.mock.calls.find(call => call[0] === 'open')[1];
                                                                          ~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:73:22 - error TS2551: Property 'createGame' does not exist on type '{ connect: () => void; disconnect: () => void; send: (message: NetworkMessage) => boolean; joinArena: (arenaId: number, playerName: string, teamId?: number | undefined) => void; leaveArena: () => void; startGame: () => void; ... 21 more ...; lastErrorCode: number | null; }'. Did you mean 'createTeam'?

73       result.current.createGame(GameMode.TEAM_DEATHMATCH);
                        ~~~~~~~~~~

  client/src/hooks/useMultiplayer.ts:964:5
    964     createTeam,
            ~~~~~~~~~~
    'createTeam' is declared here.

client/src/__tests__/hooks/useMultiplayer.test.tsx:73:42 - error TS2339: Property 'TEAM_DEATHMATCH' does not exist on type 'typeof GameMode'.

73       result.current.createGame(GameMode.TEAM_DEATHMATCH);
                                            ~~~~~~~~~~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:77:5 - error TS2304: Cannot find name 'expect'.

77     expect(mockWebSocket.send).toHaveBeenCalledWith(
       ~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:78:7 - error TS2304: Cannot find name 'expect'.

78       expect.stringContaining(MessageType.CREATE_GAME)
         ~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:78:43 - error TS2339: Property 'CREATE_GAME' does not exist on type 'typeof MessageType'.

78       expect.stringContaining(MessageType.CREATE_GAME)
                                             ~~~~~~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:82:3 - error TS2593: Cannot find name 'test'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha` and then add 'jest' or 'mocha' to the types field in your tsconfig.

82   test('should join an existing game', () => {
     ~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:83:41 - error TS2554: Expected 1 arguments, but got 0.

83     const { result } = renderHook(() => useMultiplayer());
                                           ~~~~~~~~~~~~~~

  client/src/hooks/useMultiplayer.ts:51:32
    51 export function useMultiplayer(options: MultiplayerOptions) {
                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~
    An argument for 'options' was not provided.

client/src/__tests__/hooks/useMultiplayer.test.tsx:87:30 - error TS2554: Expected 0 arguments, but got 1.

87       result.current.connect('Player2');
                                ~~~~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:91:72 - error TS7006: Parameter 'call' implicitly has an 'any' type.

91     const openHandler = mockWebSocket.addEventListener.mock.calls.find(call => call[0] === 'open')[1];
                                                                          ~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:98:22 - error TS2339: Property 'joinGame' does not exist on type '{ connect: () => void; disconnect: () => void; send: (message: NetworkMessage) => boolean; joinArena: (arenaId: number, playerName: string, teamId?: number | undefined) => void; leaveArena: () => void; startGame: () => void; ... 21 more ...; lastErrorCode: number | null; }'.

98       result.current.joinGame('game-123');
                        ~~~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:102:5 - error TS2304: Cannot find name 'expect'.

102     expect(mockWebSocket.send).toHaveBeenCalledWith(
        ~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:103:7 - error TS2304: Cannot find name 'expect'.

103       expect.stringContaining(MessageType.JOIN_GAME)
          ~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:103:43 - error TS2339: Property 'JOIN_GAME' does not exist on type 'typeof MessageType'.

103       expect.stringContaining(MessageType.JOIN_GAME)
                                              ~~~~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:107:3 - error TS2593: Cannot find name 'test'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha` and then add 'jest' or 'mocha' to the types field in your tsconfig.

107   test('should handle player movement updates', () => {
      ~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:108:41 - error TS2554: Expected 1 arguments, but got 0.

108     const { result } = renderHook(() => useMultiplayer());
                                            ~~~~~~~~~~~~~~

  client/src/hooks/useMultiplayer.ts:51:32
    51 export function useMultiplayer(options: MultiplayerOptions) {
                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~
    An argument for 'options' was not provided.

client/src/__tests__/hooks/useMultiplayer.test.tsx:112:30 - error TS2554: Expected 0 arguments, but got 1.

112       result.current.connect('Player1');
                                 ~~~~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:116:72 - error TS7006: Parameter 'call' implicitly has an 'any' type.

116     const openHandler = mockWebSocket.addEventListener.mock.calls.find(call => call[0] === 'open')[1];
                                                                           ~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:123:22 - error TS2339: Property 'updatePlayerPosition' does not exist on type '{ connect: () => void; disconnect: () => void; send: (message: NetworkMessage) => boolean; joinArena: (arenaId: number, playerName: string, teamId?: number | undefined) => void; leaveArena: () => void; startGame: () => void; ... 21 more ...; lastErrorCode: number | null; }'.

123       result.current.updatePlayerPosition({ x: 10, y: 5, z: 20 });
                         ~~~~~~~~~~~~~~~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:127:5 - error TS2304: Cannot find name 'expect'.

127     expect(mockWebSocket.send).toHaveBeenCalledWith(
        ~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:128:7 - error TS2304: Cannot find name 'expect'.

128       expect.stringContaining(MessageType.PLAYER_UPDATE)
          ~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:128:43 - error TS2551: Property 'PLAYER_UPDATE' does not exist on type 'typeof MessageType'. Did you mean 'PlayerUpdate'?

128       expect.stringContaining(MessageType.PLAYER_UPDATE)
                                              ~~~~~~~~~~~~~

  shared/schema.ts:176:3
    176   PlayerUpdate = 'player_update',
          ~~~~~~~~~~~~
    'PlayerUpdate' is declared here.

client/src/__tests__/hooks/useMultiplayer.test.tsx:132:3 - error TS2593: Cannot find name 'test'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha` and then add 'jest' or 'mocha' to the types field in your tsconfig.

132   test('should handle weapon effect creation', () => {
      ~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:133:41 - error TS2554: Expected 1 arguments, but got 0.

133     const { result } = renderHook(() => useMultiplayer());
                                            ~~~~~~~~~~~~~~

  client/src/hooks/useMultiplayer.ts:51:32
    51 export function useMultiplayer(options: MultiplayerOptions) {
                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~
    An argument for 'options' was not provided.

client/src/__tests__/hooks/useMultiplayer.test.tsx:137:30 - error TS2554: Expected 0 arguments, but got 1.

137       result.current.connect('Player1');
                                 ~~~~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:141:72 - error TS7006: Parameter 'call' implicitly has an 'any' type.

141     const openHandler = mockWebSocket.addEventListener.mock.calls.find(call => call[0] === 'open')[1];
                                                                           ~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:160:5 - error TS2304: Cannot find name 'expect'.

160     expect(mockWebSocket.send).toHaveBeenCalledWith(
        ~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:161:7 - error TS2304: Cannot find name 'expect'.

161       expect.stringContaining(MessageType.WEAPON_EFFECT)
          ~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:161:43 - error TS2339: Property 'WEAPON_EFFECT' does not exist on type 'typeof MessageType'.

161       expect.stringContaining(MessageType.WEAPON_EFFECT)
                                              ~~~~~~~~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:165:3 - error TS2593: Cannot find name 'test'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha` and then add 'jest' or 'mocha' to the types field in your tsconfig.

165   test('should handle incoming game state updates', () => {
      ~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:166:41 - error TS2554: Expected 1 arguments, but got 0.

166     const { result } = renderHook(() => useMultiplayer());
                                            ~~~~~~~~~~~~~~

  client/src/hooks/useMultiplayer.ts:51:32
    51 export function useMultiplayer(options: MultiplayerOptions) {
                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~
    An argument for 'options' was not provided.

client/src/__tests__/hooks/useMultiplayer.test.tsx:170:30 - error TS2554: Expected 0 arguments, but got 1.

170       result.current.connect('Player1');
                                 ~~~~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:174:75 - error TS7006: Parameter 'call' implicitly has an 'any' type.

174     const messageHandler = mockWebSocket.addEventListener.mock.calls.find(call => call[0] === 'message')[1];
                                                                              ~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:178:25 - error TS2551: Property 'GAME_STATE' does not exist on type 'typeof MessageType'. Did you mean 'GameStart'?

178       type: MessageType.GAME_STATE,
                            ~~~~~~~~~~

  shared/schema.ts:177:3
    177   GameStart = 'game_start',
          ~~~~~~~~~
    'GameStart' is declared here.

client/src/__tests__/hooks/useMultiplayer.test.tsx:181:24 - error TS2339: Property 'TEAM_DEATHMATCH' does not exist on type 'typeof GameMode'.

181         mode: GameMode.TEAM_DEATHMATCH,
                           ~~~~~~~~~~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:197:5 - error TS2304: Cannot find name 'expect'.

197     expect(result.current.gameState).toEqual(gameStateUpdate.data);
        ~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:200:3 - error TS2593: Cannot find name 'test'. Do you need to install type definitions for a test runner? Try `npm i --save-dev @types/jest` or `npm i --save-dev @types/mocha` and then add 'jest' or 'mocha' to the types field in your tsconfig.

200   test('should disconnect from WebSocket server', () => {
      ~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:201:41 - error TS2554: Expected 1 arguments, but got 0.

201     const { result } = renderHook(() => useMultiplayer());
                                            ~~~~~~~~~~~~~~

  client/src/hooks/useMultiplayer.ts:51:32
    51 export function useMultiplayer(options: MultiplayerOptions) {
                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~
    An argument for 'options' was not provided.

client/src/__tests__/hooks/useMultiplayer.test.tsx:205:30 - error TS2554: Expected 0 arguments, but got 1.

205       result.current.connect('Player1');
                                 ~~~~~~~~~

client/src/__tests__/hooks/useMultiplayer.test.tsx:214:5 - error TS2304: Cannot find name 'expect'.

214     expect(mockWebSocket.close).toHaveBeenCalled();
        ~~~~~~

client/src/components/AIPetGenerationDialog.tsx:9:8 - error TS2307: Cannot find module '@/components/ui/dialog' or its corresponding type declarations.

9 } from '@/components/ui/dialog';
         ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/AIPetGenerationDialog.tsx:10:24 - error TS2307: Cannot find module '@/components/ui/button' or its corresponding type declarations.

10 import { Button } from '@/components/ui/button';
                          ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/AIPetGenerationDialog.tsx:11:23 - error TS2307: Cannot find module '@/components/ui/input' or its corresponding type declarations.

11 import { Input } from '@/components/ui/input';
                         ~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/AIPetGenerationDialog.tsx:12:23 - error TS2307: Cannot find module '@/components/ui/label' or its corresponding type declarations.

12 import { Label } from '@/components/ui/label';
                         ~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/AIPetGenerationDialog.tsx:13:26 - error TS2307: Cannot find module '@/components/ui/textarea' or its corresponding type declarations.

13 import { Textarea } from '@/components/ui/textarea';
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/AIPetGenerationDialog.tsx:14:79 - error TS2307: Cannot find module '@/components/ui/select' or its corresponding type declarations.

14 import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
                                                                                 ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/AIPetGenerationDialog.tsx:15:26 - error TS2307: Cannot find module '@/hooks/use-toast' or its corresponding type declarations.

15 import { useToast } from '@/hooks/use-toast';
                            ~~~~~~~~~~~~~~~~~~~

client/src/components/AIPetGenerationDialog.tsx:17:25 - error TS2307: Cannot find module '@/contexts/Web3Context' or its corresponding type declarations.

17 import { useWeb3 } from '@/contexts/Web3Context';
                           ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/AIPetGenerationDialog.tsx:18:28 - error TS2307: Cannot find module '@/services/petService' or its corresponding type declarations.

18 import { PetService } from '@/services/petService';
                              ~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/AIPetGenerationDialog.tsx:19:37 - error TS2307: Cannot find module '@/services/aiGenerationService' or its corresponding type declarations.

19 import { AIGenerationService } from '@/services/aiGenerationService';
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/AIPetGenerationDialog.tsx:402:26 - error TS7006: Parameter 'e' implicitly has an 'any' type.

402               onChange={(e) => setPetName(e.target.value)}
                             ~

client/src/components/AIPetGenerationDialog.tsx:440:28 - error TS7006: Parameter 'e' implicitly has an 'any' type.

440                 onChange={(e) => setColor(e.target.value)}
                               ~

client/src/components/AIPetGenerationDialog.tsx:445:28 - error TS7006: Parameter 'e' implicitly has an 'any' type.

445                 onChange={(e) => setColor(e.target.value)}
                               ~

client/src/components/AIPetGenerationDialog.tsx:481:26 - error TS7006: Parameter 'e' implicitly has an 'any' type.

481               onChange={(e) => setDescription(e.target.value)}
                             ~

client/src/components/AuthProtection.tsx:3:25 - error TS2307: Cannot find module '@/contexts/AuthContext' or its corresponding type declarations.

3 import { useAuth } from '@/contexts/AuthContext';
                          ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/DualLoginButton.tsx:2:24 - error TS2307: Cannot find module '@/components/ui/button' or its corresponding type declarations.

2 import { Button } from '@/components/ui/button';
                         ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/DualLoginButton.tsx:3:25 - error TS2307: Cannot find module '@/contexts/Web3Context' or its corresponding type declarations.

3 import { useWeb3 } from '@/contexts/Web3Context';
                          ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/DualLoginButton.tsx:4:25 - error TS2307: Cannot find module '@/contexts/AuthContext' or its corresponding type declarations.

4 import { useAuth } from '@/contexts/AuthContext';
                          ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/DualLoginButton.tsx:6:74 - error TS2307: Cannot find module '@/components/ui/tooltip' or its corresponding type declarations.

6 import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
                                                                           ~~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/NFTBasedPetDialog.tsx:9:8 - error TS2307: Cannot find module '@/components/ui/dialog' or its corresponding type declarations.

9 } from '@/components/ui/dialog';
         ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/NFTBasedPetDialog.tsx:10:24 - error TS2307: Cannot find module '@/components/ui/button' or its corresponding type declarations.

10 import { Button } from '@/components/ui/button';
                          ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/NFTBasedPetDialog.tsx:11:26 - error TS2307: Cannot find module '@/hooks/use-toast' or its corresponding type declarations.

11 import { useToast } from '@/hooks/use-toast';
                            ~~~~~~~~~~~~~~~~~~~

client/src/components/NFTBasedPetDialog.tsx:13:25 - error TS2307: Cannot find module '@/contexts/Web3Context' or its corresponding type declarations.

13 import { useWeb3 } from '@/contexts/Web3Context';
                           ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/NFTBasedPetDialog.tsx:14:28 - error TS2307: Cannot find module '@/services/petService' or its corresponding type declarations.

14 import { PetService } from '@/services/petService';
                              ~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/NFTBasedPetDialog.tsx:15:37 - error TS2307: Cannot find module '@/services/petGeneratorService' or its corresponding type declarations.

15 import { PetGeneratorService } from '@/services/petGeneratorService';
                                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/NFTBrowser.tsx:2:25 - error TS2307: Cannot find module '@/contexts/Web3Context' or its corresponding type declarations.

2 import { useWeb3 } from '@/contexts/Web3Context';
                          ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/NFTBrowser.tsx:3:26 - error TS2307: Cannot find module '@/hooks/use-toast' or its corresponding type declarations.

3 import { useToast } from '@/hooks/use-toast';
                           ~~~~~~~~~~~~~~~~~~~

client/src/components/NFTBrowser.tsx:4:10 - error TS2305: Module '"ethers"' has no exported member 'ethers'.

4 import { ethers } from 'ethers';
           ~~~~~~

client/src/components/NFTBrowser.tsx:12:8 - error TS2307: Cannot find module '@/components/ui/dialog' or its corresponding type declarations.

12 } from '@/components/ui/dialog';
          ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/NFTBrowser.tsx:13:24 - error TS2307: Cannot find module '@/components/ui/button' or its corresponding type declarations.

13 import { Button } from '@/components/ui/button';
                          ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/NFTBrowser.tsx:335:32 - error TS7006: Parameter 'e' implicitly has an 'any' type.

335         onPointerDownOutside={(e) => e.preventDefault()}
                                   ~

client/src/components/NFTBrowser.tsx:336:29 - error TS7006: Parameter 'e' implicitly has an 'any' type.

336         onInteractOutside={(e) => e.preventDefault()}
                                ~

client/src/components/NFTBrowser.tsx:337:27 - error TS7006: Parameter 'e' implicitly has an 'any' type.

337         onEscapeKeyDown={(e) => e.preventDefault()}>
                              ~

client/src/components/NFTMintDialog.tsx:9:8 - error TS2307: Cannot find module '@/components/ui/dialog' or its corresponding type declarations.

9 } from '@/components/ui/dialog';
         ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/NFTMintDialog.tsx:10:24 - error TS2307: Cannot find module '@/components/ui/button' or its corresponding type declarations.

10 import { Button } from '@/components/ui/button';
                          ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/NFTMintDialog.tsx:11:23 - error TS2307: Cannot find module '@/components/ui/input' or its corresponding type declarations.

11 import { Input } from '@/components/ui/input';
                         ~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/NFTMintDialog.tsx:12:23 - error TS2307: Cannot find module '@/components/ui/label' or its corresponding type declarations.

12 import { Label } from '@/components/ui/label';
                         ~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/NFTMintDialog.tsx:13:25 - error TS2307: Cannot find module '@/contexts/Web3Context' or its corresponding type declarations.

13 import { useWeb3 } from '@/contexts/Web3Context';
                           ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/NFTMintDialog.tsx:14:26 - error TS2307: Cannot find module '@/hooks/use-toast' or its corresponding type declarations.

14 import { useToast } from '@/hooks/use-toast';
                            ~~~~~~~~~~~~~~~~~~~

client/src/components/NFTMintDialog.tsx:15:28 - error TS2307: Cannot find module '@/services/petService' or its corresponding type declarations.

15 import { PetService } from '@/services/petService';
                              ~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/NFTMintDialog.tsx:17:42 - error TS2307: Cannot find module '@/game/types' or its corresponding type declarations.

17 import { SpecterType, SpecterTier } from '@/game/types';
                                            ~~~~~~~~~~~~~~

client/src/components/NFTMintDialogProvider.tsx:3:29 - error TS2307: Cannot find module '@/game/types' or its corresponding type declarations.

3 import { SpecterType } from '@/game/types';
                              ~~~~~~~~~~~~~~

client/src/components/PVPArenaDialog.tsx:117:53 - error TS7006: Parameter 't' implicitly has an 'any' type.

117       if (selectedTournamentId && !tournaments.some(t => t.id === selectedTournamentId)) {
                                                        ~

client/src/components/TournamentBattleDialog.tsx:7:8 - error TS2307: Cannot find module '@/components/ui/dialog' or its corresponding type declarations.

7 } from '@/components/ui/dialog';
         ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/TournamentBattleDialog.tsx:9:32 - error TS2307: Cannot find module '@/hooks/useMultiplayer' or its corresponding type declarations.

9 import { useMultiplayer } from '@/hooks/useMultiplayer';
                                 ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/TournamentBattleDialog.tsx:10:38 - error TS2307: Cannot find module '@/game/battle/TestTournamentBattle' or its corresponding type declarations.

10 import { TestTournamentBattle } from '@/game/battle/TestTournamentBattle';
                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/TournamentBattleDialog.tsx:11:35 - error TS2307: Cannot find module '@shared/schema' or its corresponding type declarations.

11 import { RenderInstruction } from '@shared/schema';
                                     ~~~~~~~~~~~~~~~~

client/src/components/TournamentDialog.tsx:9:8 - error TS2307: Cannot find module '@/components/ui/dialog' or its corresponding type declarations.

9 } from '@/components/ui/dialog';
         ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/TournamentDialog.tsx:10:24 - error TS2307: Cannot find module '@/components/ui/button' or its corresponding type declarations.

10 import { Button } from '@/components/ui/button';
                          ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/TournamentDialog.tsx:11:25 - error TS2307: Cannot find module '@/contexts/Web3Context' or its corresponding type declarations.

11 import { useWeb3 } from '@/contexts/Web3Context';
                           ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/TournamentDialog.tsx:12:26 - error TS2307: Cannot find module '@/hooks/use-toast' or its corresponding type declarations.

12 import { useToast } from '@/hooks/use-toast';
                            ~~~~~~~~~~~~~~~~~~~

client/src/components/TrainingFeeDialog.tsx:9:8 - error TS2307: Cannot find module '@/components/ui/dialog' or its corresponding type declarations.

9 } from '@/components/ui/dialog';
         ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/TrainingFeeDialog.tsx:10:24 - error TS2307: Cannot find module '@/components/ui/button' or its corresponding type declarations.

10 import { Button } from '@/components/ui/button';
                          ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/TrainingFeeDialog.tsx:11:25 - error TS2307: Cannot find module '@/contexts/Web3Context' or its corresponding type declarations.

11 import { useWeb3 } from '@/contexts/Web3Context';
                           ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/TrainingFeeDialog.tsx:12:26 - error TS2307: Cannot find module '@/hooks/use-toast' or its corresponding type declarations.

12 import { useToast } from '@/hooks/use-toast';
                            ~~~~~~~~~~~~~~~~~~~

client/src/components/TrainingFeeDialog.tsx:14:34 - error TS2307: Cannot find module '@/game/entities/PetSpecter' or its corresponding type declarations.

14 import { SpecterTraitType } from '@/game/entities/PetSpecter';
                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/WalletConnectButton.tsx:2:24 - error TS2307: Cannot find module '@/components/ui/button' or its corresponding type declarations.

2 import { Button } from '@/components/ui/button';
                         ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/WalletConnectButton.tsx:3:25 - error TS2307: Cannot find module '@/contexts/Web3Context' or its corresponding type declarations.

3 import { useWeb3 } from '@/contexts/Web3Context';
                          ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/WalletConnectButton.tsx:5:74 - error TS2307: Cannot find module '@/components/ui/tooltip' or its corresponding type declarations.

5 import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
                                                                           ~~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/ui/accordion.tsx:5:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

5 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/alert-dialog.tsx:4:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

4 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/alert-dialog.tsx:5:32 - error TS2307: Cannot find module '@/components/ui/button' or its corresponding type declarations.

5 import { buttonVariants } from "@/components/ui/button"
                                 ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/ui/alert.tsx:4:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

4 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/avatar.tsx:4:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

4 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/badge.tsx:4:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

4 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/breadcrumb.tsx:5:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

5 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/button.tsx:5:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

5 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/calendar.tsx:5:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

5 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/calendar.tsx:6:32 - error TS2307: Cannot find module '@/components/ui/button' or its corresponding type declarations.

6 import { buttonVariants } from "@/components/ui/button"
                                 ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/ui/card.tsx:3:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

3 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/carousel.tsx:7:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

7 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/carousel.tsx:8:24 - error TS2307: Cannot find module '@/components/ui/button' or its corresponding type declarations.

8 import { Button } from "@/components/ui/button"
                         ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/ui/chart.tsx:4:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

4 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/checkbox.tsx:5:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

5 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/command.tsx:6:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

6 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/command.tsx:7:39 - error TS2307: Cannot find module '@/components/ui/dialog' or its corresponding type declarations.

7 import { Dialog, DialogContent } from "@/components/ui/dialog"
                                        ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/ui/context-menu.tsx:5:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

5 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/dialog.tsx:5:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

5 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/drawer.tsx:4:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

4 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/dropdown-menu.tsx:5:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

5 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/form.tsx:13:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

13 import { cn } from "@/lib/utils"
                      ~~~~~~~~~~~~~

client/src/components/ui/form.tsx:14:23 - error TS2307: Cannot find module '@/components/ui/label' or its corresponding type declarations.

14 import { Label } from "@/components/ui/label"
                         ~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/ui/hover-card.tsx:4:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

4 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/input-otp.tsx:5:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

5 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/input.tsx:3:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

3 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/label.tsx:5:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

5 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/menubar.tsx:5:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

5 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/navigation-menu.tsx:6:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

6 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/pagination.tsx:4:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

4 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/pagination.tsx:5:45 - error TS2307: Cannot find module '@/components/ui/button' or its corresponding type declarations.

5 import { ButtonProps, buttonVariants } from "@/components/ui/button"
                                              ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/ui/pagination.tsx:68:5 - error TS2783: 'size' is specified more than once, so this usage will be overwritten.

68     size="default"
       ~~~~~~~~~~~~~~

  client/src/components/ui/pagination.tsx:70:5
    70     {...props}
           ~~~~~~~~~~
    This spread always overwrites this property.

client/src/components/ui/pagination.tsx:84:5 - error TS2783: 'size' is specified more than once, so this usage will be overwritten.

84     size="default"
       ~~~~~~~~~~~~~~

  client/src/components/ui/pagination.tsx:86:5
    86     {...props}
           ~~~~~~~~~~
    This spread always overwrites this property.

client/src/components/ui/popover.tsx:4:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

4 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/progress.tsx:4:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

4 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/radio-group.tsx:5:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

5 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/resizable.tsx:4:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

4 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/scroll-area.tsx:4:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

4 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/select.tsx:5:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

5 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/separator.tsx:4:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

4 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/sheet.tsx:6:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

6 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/sidebar.tsx:6:29 - error TS2307: Cannot find module '@/hooks/use-mobile' or its corresponding type declarations.

6 import { useIsMobile } from "@/hooks/use-mobile"
                              ~~~~~~~~~~~~~~~~~~~~

client/src/components/ui/sidebar.tsx:7:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

7 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/sidebar.tsx:8:24 - error TS2307: Cannot find module '@/components/ui/button' or its corresponding type declarations.

8 import { Button } from "@/components/ui/button"
                         ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/ui/sidebar.tsx:9:23 - error TS2307: Cannot find module '@/components/ui/input' or its corresponding type declarations.

9 import { Input } from "@/components/ui/input"
                        ~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/ui/sidebar.tsx:10:27 - error TS2307: Cannot find module '@/components/ui/separator' or its corresponding type declarations.

10 import { Separator } from "@/components/ui/separator"
                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/ui/sidebar.tsx:11:37 - error TS2307: Cannot find module '@/components/ui/sheet' or its corresponding type declarations.

11 import { Sheet, SheetContent } from "@/components/ui/sheet"
                                       ~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/ui/sidebar.tsx:12:26 - error TS2307: Cannot find module '@/components/ui/skeleton' or its corresponding type declarations.

12 import { Skeleton } from "@/components/ui/skeleton"
                            ~~~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/ui/sidebar.tsx:18:8 - error TS2307: Cannot find module '@/components/ui/tooltip' or its corresponding type declarations.

18 } from "@/components/ui/tooltip"
          ~~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/ui/sidebar.tsx:274:17 - error TS7006: Parameter 'event' implicitly has an 'any' type.

274       onClick={(event) => {
                    ~~~~~

client/src/components/ui/skeleton.tsx:1:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

1 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/slider.tsx:4:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

4 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/switch.tsx:4:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

4 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/table.tsx:3:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

3 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/tabs.tsx:4:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

4 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/textarea.tsx:3:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

3 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/toast.tsx:6:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

6 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/toaster.tsx:1:26 - error TS2307: Cannot find module '@/hooks/use-toast' or its corresponding type declarations.

1 import { useToast } from "@/hooks/use-toast"
                           ~~~~~~~~~~~~~~~~~~~

client/src/components/ui/toaster.tsx:9:8 - error TS2307: Cannot find module '@/components/ui/toast' or its corresponding type declarations.

9 } from "@/components/ui/toast"
         ~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/ui/toaster.tsx:16:31 - error TS7031: Binding element 'id' implicitly has an 'any' type.

16       {toasts.map(function ({ id, title, description, action, ...props }) {
                                 ~~

client/src/components/ui/toaster.tsx:16:35 - error TS7031: Binding element 'title' implicitly has an 'any' type.

16       {toasts.map(function ({ id, title, description, action, ...props }) {
                                     ~~~~~

client/src/components/ui/toaster.tsx:16:42 - error TS7031: Binding element 'description' implicitly has an 'any' type.

16       {toasts.map(function ({ id, title, description, action, ...props }) {
                                            ~~~~~~~~~~~

client/src/components/ui/toaster.tsx:16:55 - error TS7031: Binding element 'action' implicitly has an 'any' type.

16       {toasts.map(function ({ id, title, description, action, ...props }) {
                                                         ~~~~~~

client/src/components/ui/toggle-group.tsx:5:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

5 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/toggle-group.tsx:6:32 - error TS2307: Cannot find module '@/components/ui/toggle' or its corresponding type declarations.

6 import { toggleVariants } from "@/components/ui/toggle"
                                 ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/components/ui/toggle-group.tsx:20:4 - error TS2322: Type '{ children: Element; ref: ForwardedRef<HTMLDivElement>; className: any; } | { children: Element; ref: ForwardedRef<HTMLDivElement>; className: any; }' is not assignable to type 'IntrinsicAttributes & ((ToggleGroupSingleProps | ToggleGroupMultipleProps) & RefAttributes<HTMLDivElement>)'.
  Type '{ children: Element; ref: ForwardedRef<HTMLDivElement>; className: any; }' is not assignable to type 'IntrinsicAttributes & ((ToggleGroupSingleProps | ToggleGroupMultipleProps) & RefAttributes<HTMLDivElement>)'.
    Property 'type' is missing in type '{ children: Element; ref: ForwardedRef<HTMLDivElement>; className: any; }' but required in type 'ToggleGroupMultipleProps'.

20   <ToggleGroupPrimitive.Root
      ~~~~~~~~~~~~~~~~~~~~~~~~~

  node_modules/@radix-ui/react-toggle-group/dist/index.d.mts:12:5
    12     type: 'multiple';
           ~~~~
    'type' is declared here.

client/src/components/ui/toggle-group.tsx:41:6 - error TS2741: Property 'value' is missing in type '{ children: any; ref: ForwardedRef<HTMLButtonElement>; className: any; }' but required in type 'Omit<ToggleGroupItemImplProps, "pressed">'.

41     <ToggleGroupPrimitive.Item
        ~~~~~~~~~~~~~~~~~~~~~~~~~

  node_modules/@radix-ui/react-toggle-group/dist/index.d.mts:70:5
    70     value: string;
           ~~~~~
    'value' is declared here.

client/src/components/ui/toggle.tsx:5:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

5 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/components/ui/tooltip.tsx:4:20 - error TS2307: Cannot find module '@/lib/utils' or its corresponding type declarations.

4 import { cn } from "@/lib/utils"
                     ~~~~~~~~~~~~~

client/src/contexts/AuthContext.tsx:2:25 - error TS2307: Cannot find module '@/contexts/Web3Context' or its corresponding type declarations.

2 import { useWeb3 } from '@/contexts/Web3Context'; // Import useWeb3
                          ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/contexts/PvpAccessContext.tsx:2:25 - error TS2835: Relative import paths need explicit file extensions in ECMAScript imports when '--moduleResolution' is 'node16' or 'nodenext'. Did you mean './AuthContext.jsx'?

2 import { useAuth } from './AuthContext';
                          ~~~~~~~~~~~~~~~

client/src/contexts/Web3Context.tsx:6:26 - error TS2307: Cannot find module '@/hooks/use-toast' or its corresponding type declarations.

6 import { useToast } from '@/hooks/use-toast';
                           ~~~~~~~~~~~~~~~~~~~

client/src/contexts/Web3Context.tsx:7:28 - error TS2307: Cannot find module '@/services/petService' or its corresponding type declarations.

7 import { PetService } from '@/services/petService';
                             ~~~~~~~~~~~~~~~~~~~~~~~

client/src/game/battle/BattleManager.ts:87:7 - error TS2322: Type 'string' is not assignable to type 'number'.

87       sourceId: pet.id,
         ~~~~~~~~

  client/src/game/battle/BattleManager.ts:32:3
    32   sourceId?: number;
         ~~~~~~~~
    The expected type comes from property 'sourceId' which is declared here on type 'BattleEvent'

client/src/game/battle/BattleManager.ts:126:45 - error TS2554: Expected 1 arguments, but got 3.

126       AudioManager.playSound('battleMusic', 0.5, true);
                                                ~~~~~~~~~

client/src/game/battle/BattleManager.ts:140:22 - error TS2345: Argument of type 'string | null' is not assignable to parameter of type 'number | null'.
  Type 'string' is not assignable to type 'number'.

140       this.endBattle(alivePets.length === 1 ? alivePets[0].id : null);
                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

client/src/game/battle/BattleManager.ts:166:40 - error TS2445: Property 'mesh' is protected and only accessible within class 'Entity' and its subclasses.

166         p !== pet && p.health > 0 && p.mesh && this.scene.getObjectById(p.mesh.id)
                                           ~~~~

client/src/game/battle/BattleManager.ts:166:75 - error TS2445: Property 'mesh' is protected and only accessible within class 'Entity' and its subclasses.

166         p !== pet && p.health > 0 && p.mesh && this.scene.getObjectById(p.mesh.id)
                                                                              ~~~~

client/src/game/battle/BattleManager.ts:174:20 - error TS2445: Property 'mesh' is protected and only accessible within class 'Entity' and its subclasses.

174         if (target.mesh) {
                       ~~~~

client/src/game/battle/BattleManager.ts:175:38 - error TS2445: Property 'mesh' is protected and only accessible within class 'Entity' and its subclasses.

175           pet.currentTarget = target.mesh;
                                         ~~~~

client/src/game/battle/BattleManager.ts:181:13 - error TS2322: Type 'string' is not assignable to type 'number'.

181             sourceId: pet.id,
                ~~~~~~~~

  client/src/game/battle/BattleManager.ts:32:3
    32   sourceId?: number;
         ~~~~~~~~
    The expected type comes from property 'sourceId' which is declared here on type 'BattleEvent'

client/src/game/battle/BattleManager.ts:182:13 - error TS2322: Type 'string' is not assignable to type 'number'.

182             targetId: target.id,
                ~~~~~~~~

  client/src/game/battle/BattleManager.ts:33:3
    33   targetId?: number;
         ~~~~~~~~
    The expected type comes from property 'targetId' which is declared here on type 'BattleEvent'

client/src/game/battle/BattleManager.ts:201:13 - error TS2445: Property 'mesh' is protected and only accessible within class 'Entity' and its subclasses.

201       if (p.mesh && p.mesh.id === target.id && p !== pet && p.health > 0) {
                ~~~~

client/src/game/battle/BattleManager.ts:201:23 - error TS2445: Property 'mesh' is protected and only accessible within class 'Entity' and its subclasses.

201       if (p.mesh && p.mesh.id === target.id && p !== pet && p.health > 0) {
                          ~~~~

client/src/game/battle/BattleManager.ts:223:63 - error TS2367: This comparison appears to be unintentional because the types 'string' and 'number' have no overlap.

223     const winnerPet = winnerId !== null ? this.pets.find(p => p.id === winnerId) : null;
                                                                  ~~~~~~~~~~~~~~~~~

client/src/game/battle/BattleManager.ts:233:18 - error TS2339: Property 'stopSound' does not exist on type 'AudioManager'.

233     AudioManager.stopSound('battleMusic');
                     ~~~~~~~~~

client/src/game/battle/BattleManager.ts:243:7 - error TS2322: Type 'string[]' is not assignable to type 'number[]'.
  Type 'string' is not assignable to type 'number'.

243       loserIds: this.pets.filter(p => p.id !== winnerId).map(p => p.id),
          ~~~~~~~~

  client/src/game/battle/BattleManager.ts:21:3
    21   loserIds: number[];
         ~~~~~~~~
    The expected type comes from property 'loserIds' which is declared here on type 'BattleResult'

client/src/game/battle/BattleManager.ts:243:39 - error TS2367: This comparison appears to be unintentional because the types 'string' and 'number | null' have no overlap.

243       loserIds: this.pets.filter(p => p.id !== winnerId).map(p => p.id),
                                          ~~~~~~~~~~~~~~~~~

client/src/game/battle/TestTournamentBattle.ts:1:71 - error TS2307: Cannot find module '@shared/schema' or its corresponding type declarations.

1 import { MessageType, RenderInstruction, RenderInstructionType } from '@shared/schema';
                                                                        ~~~~~~~~~~~~~~~~

client/src/game/battle/TournamentBattleRenderer.ts:3:58 - error TS2307: Cannot find module '@shared/schema' or its corresponding type declarations.

3 import { RenderInstruction, RenderInstructionType } from '@shared/schema';
                                                           ~~~~~~~~~~~~~~~~

client/src/game/battle/TournamentBattleRenderer.ts:97:7 - error TS2345: Argument of type 'string' is not assignable to parameter of type 'Scene'.

97       String(petData.id),
         ~~~~~~~~~~~~~~~~~~

client/src/game/battle/TournamentBattleRenderer.ts:114:11 - error TS2339: Property 'setRotation' does not exist on type 'PetSpecter'.

114       pet.setRotation(new THREE.Euler(
              ~~~~~~~~~~~

client/src/game/battle/TournamentBattleRenderer.ts:129:44 - error TS2554: Expected 1 arguments, but got 2.

129     audioManager.playSound('specterSpawn', 0.5);
                                               ~~~

client/src/game/battle/TournamentBattleRenderer.ts:166:11 - error TS2339: Property 'setRotation' does not exist on type 'PetSpecter'.

166       pet.setRotation(new THREE.Euler(
              ~~~~~~~~~~~

client/src/game/battle/TournamentBattleRenderer.ts:209:38 - error TS2554: Expected 1 arguments, but got 2.

209     audioManager.playSound('attack', 0.5);
                                         ~~~

client/src/game/battle/TournamentBattleRenderer.ts:214:15 - error TS2339: Property 'setRotation' does not exist on type 'PetSpecter'.

214     sourcePet.setRotation(new THREE.Euler(0, angle, 0));
                  ~~~~~~~~~~~

client/src/game/battle/TournamentBattleRenderer.ts:275:38 - error TS2554: Expected 1 arguments, but got 2.

275     audioManager.playSound('shield', 0.5);
                                         ~~~

client/src/game/battle/TournamentBattleRenderer.ts:315:49 - error TS2554: Expected 1 arguments, but got 2.

315         audioManager.playSound('specialAttack', 0.7);
                                                    ~~~

client/src/game/battle/TournamentBattleRenderer.ts:332:43 - error TS2554: Expected 1 arguments, but got 2.

332         audioManager.playSound('victory', 0.7);
                                              ~~~

client/src/game/battle/TournamentBattleRenderer.ts:371:38 - error TS2554: Expected 1 arguments, but got 2.

371     audioManager.playSound('damage', 0.5);
                                         ~~~

client/src/game/battle/TournamentBattleRenderer.ts:385:18 - error TS2339: Property 'material' does not exist on type 'Group<Object3DEventMap> | Mesh<BufferGeometry<NormalBufferAttributes>, Material | Material[], Object3DEventMap>'.
  Property 'material' does not exist on type 'Group<Object3DEventMap>'.

385         if (mesh.material instanceof THREE.Material) {
                     ~~~~~~~~

client/src/game/battle/TournamentBattleRenderer.ts:386:39 - error TS2339: Property 'material' does not exist on type 'Group<Object3DEventMap> | Mesh<BufferGeometry<NormalBufferAttributes>, Material | Material[], Object3DEventMap>'.
  Property 'material' does not exist on type 'Group<Object3DEventMap>'.

386           originalMaterials.push(mesh.material);
                                          ~~~~~~~~

client/src/game/battle/TournamentBattleRenderer.ts:387:39 - error TS2339: Property 'material' does not exist on type 'Group<Object3DEventMap> | Mesh<BufferGeometry<NormalBufferAttributes>, Material | Material[], Object3DEventMap>'.
  Property 'material' does not exist on type 'Group<Object3DEventMap>'.

387         } else if (Array.isArray(mesh.material)) {
                                          ~~~~~~~~

client/src/game/battle/TournamentBattleRenderer.ts:388:42 - error TS2339: Property 'material' does not exist on type 'Group<Object3DEventMap> | Mesh<BufferGeometry<NormalBufferAttributes>, Material | Material[], Object3DEventMap>'.
  Property 'material' does not exist on type 'Group<Object3DEventMap>'.

388           originalMaterials.push(...mesh.material);
                                             ~~~~~~~~

client/src/game/battle/TournamentBattleRenderer.ts:395:32 - error TS2339: Property 'material' does not exist on type 'Group<Object3DEventMap> | Mesh<BufferGeometry<NormalBufferAttributes>, Material | Material[], Object3DEventMap>'.
  Property 'material' does not exist on type 'Group<Object3DEventMap>'.

395         if (Array.isArray(mesh.material)) {
                                   ~~~~~~~~

client/src/game/battle/TournamentBattleRenderer.ts:396:16 - error TS2339: Property 'material' does not exist on type 'Group<Object3DEventMap> | Mesh<BufferGeometry<NormalBufferAttributes>, Material | Material[], Object3DEventMap>'.
  Property 'material' does not exist on type 'Group<Object3DEventMap>'.

396           mesh.material = Array(mesh.material.length).fill(damageMaterial);
                   ~~~~~~~~

client/src/game/battle/TournamentBattleRenderer.ts:396:38 - error TS2339: Property 'material' does not exist on type 'Group<Object3DEventMap> | Mesh<BufferGeometry<NormalBufferAttributes>, Material | Material[], Object3DEventMap>'.
  Property 'material' does not exist on type 'Group<Object3DEventMap>'.

396           mesh.material = Array(mesh.material.length).fill(damageMaterial);
                                         ~~~~~~~~

client/src/game/battle/TournamentBattleRenderer.ts:398:16 - error TS2339: Property 'material' does not exist on type 'Group<Object3DEventMap> | Mesh<BufferGeometry<NormalBufferAttributes>, Material | Material[], Object3DEventMap>'.
  Property 'material' does not exist on type 'Group<Object3DEventMap>'.

398           mesh.material = damageMaterial;
                   ~~~~~~~~

client/src/game/battle/TournamentBattleRenderer.ts:404:36 - error TS2339: Property 'material' does not exist on type 'Group<Object3DEventMap> | Mesh<BufferGeometry<NormalBufferAttributes>, Material | Material[], Object3DEventMap>'.
  Property 'material' does not exist on type 'Group<Object3DEventMap>'.

404             if (Array.isArray(mesh.material) && originalMaterials.length === mesh.material.length) {
                                       ~~~~~~~~

client/src/game/battle/TournamentBattleRenderer.ts:404:83 - error TS2339: Property 'material' does not exist on type 'Group<Object3DEventMap> | Mesh<BufferGeometry<NormalBufferAttributes>, Material | Material[], Object3DEventMap>'.
  Property 'material' does not exist on type 'Group<Object3DEventMap>'.

404             if (Array.isArray(mesh.material) && originalMaterials.length === mesh.material.length) {
                                                                                      ~~~~~~~~

client/src/game/battle/TournamentBattleRenderer.ts:405:20 - error TS2339: Property 'material' does not exist on type 'Group<Object3DEventMap> | Mesh<BufferGeometry<NormalBufferAttributes>, Material | Material[], Object3DEventMap>'.
  Property 'material' does not exist on type 'Group<Object3DEventMap>'.

405               mesh.material = [...originalMaterials];
                       ~~~~~~~~

client/src/game/battle/TournamentBattleRenderer.ts:406:44 - error TS2339: Property 'material' does not exist on type 'Group<Object3DEventMap> | Mesh<BufferGeometry<NormalBufferAttributes>, Material | Material[], Object3DEventMap>'.
  Property 'material' does not exist on type 'Group<Object3DEventMap>'.

406             } else if (!Array.isArray(mesh.material) && originalMaterials.length === 1) {
                                               ~~~~~~~~

client/src/game/battle/TournamentBattleRenderer.ts:407:20 - error TS2339: Property 'material' does not exist on type 'Group<Object3DEventMap> | Mesh<BufferGeometry<NormalBufferAttributes>, Material | Material[], Object3DEventMap>'.
  Property 'material' does not exist on type 'Group<Object3DEventMap>'.

407               mesh.material = originalMaterials[0];
                       ~~~~~~~~

client/src/game/battle/TournamentBattleRenderer.ts:446:36 - error TS2554: Expected 1 arguments, but got 2.

446     audioManager.playSound('heal', 0.5);
                                       ~~~

client/src/game/battle/TournamentBattleRenderer.ts:521:46 - error TS2554: Expected 1 arguments, but got 2.

521     audioManager.playSound('specterCapture', 0.5);
                                                 ~~~

client/src/game/effects/GravityEffect.ts:121:7 - error TS2322: Type 'boolean' is not assignable to type 'void'.

121       return true; // Indicate that the effect was applied
          ~~~~~~

client/src/game/effects/GravityEffect.ts:124:5 - error TS2322: Type 'boolean' is not assignable to type 'void'.

124     return false; // Indicate that the effect was not applied
        ~~~~~~

client/src/game/effects/ParticleSystem.ts:620:26 - error TS2339: Property 'opacity' does not exist on type 'Material | Material[]'.
  Property 'opacity' does not exist on type 'Material[]'.

620       particles.material.opacity = 1 - fadeProgress;
                             ~~~~~~~

client/src/game/engine/InputHandler.ts:3:29 - error TS2307: Cannot find module '@/hooks/use-mobile' or its corresponding type declarations.

3 import { useIsMobile } from '@/hooks/use-mobile';
                              ~~~~~~~~~~~~~~~~~~~~

client/src/game/entities/DungeonBoss.ts:3:35 - error TS2835: Relative import paths need explicit file extensions in ECMAScript imports when '--moduleResolution' is 'node16' or 'nodenext'. Did you mean '../world/DungeonGenerator.js'?

3 import { DungeonDifficulty } from '../world/DungeonGenerator';
                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~

client/src/game/entities/DungeonBoss.ts:4:30 - error TS2835: Relative import paths need explicit file extensions in ECMAScript imports when '--moduleResolution' is 'node16' or 'nodenext'. Did you mean '../utils/SeededRandom.js'?

4 import { SeededRandom } from '../utils/SeededRandom';
                               ~~~~~~~~~~~~~~~~~~~~~~~

client/src/game/entities/DungeonBoss.ts:5:46 - error TS2835: Relative import paths need explicit file extensions in ECMAScript imports when '--moduleResolution' is 'node16' or 'nodenext'. Did you mean '../audio/AudioManager.js'?

5 import { audioManager as AudioManager } from '../audio/AudioManager';
                                               ~~~~~~~~~~~~~~~~~~~~~~~

client/src/game/entities/DungeonBoss.ts:29:11 - error TS2564: Property 'health' has no initializer and is not definitely assigned in the constructor.

29   private health: number;
             ~~~~~~

client/src/game/entities/DungeonBoss.ts:30:11 - error TS2564: Property 'maxHealth' has no initializer and is not definitely assigned in the constructor.

30   private maxHealth: number;
             ~~~~~~~~~

client/src/game/entities/DungeonBoss.ts:31:11 - error TS2564: Property 'attackPower' has no initializer and is not definitely assigned in the constructor.

31   private attackPower: number;
             ~~~~~~~~~~~

client/src/game/entities/DungeonBoss.ts:32:11 - error TS2564: Property 'speed' has no initializer and is not definitely assigned in the constructor.

32   private speed: number;
             ~~~~~

client/src/game/entities/DungeonBoss.ts:40:11 - error TS2564: Property 'pointValue' has no initializer and is not definitely assigned in the constructor.

40   private pointValue: number;
             ~~~~~~~~~~

client/src/game/entities/Specter.ts:16:7 - error TS2415: Class 'Specter' incorrectly extends base class 'Entity'.
  Property 'mesh' is private in type 'Specter' but not in type 'Entity'.

16 class Specter extends Entity {
         ~~~~~~~

client/src/game/pvp/PVPArenaManager.ts:5:45 - error TS2307: Cannot find module '@shared/schema' or its corresponding type declarations.

5 import { MessageType, NetworkMessage } from '@shared/schema';
                                              ~~~~~~~~~~~~~~~~

client/src/game/ui/DungeonRewardsDialog.tsx:2:99 - error TS2307: Cannot find module '@/components/ui/dialog' or its corresponding type declarations.

2 import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
                                                                                                    ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/game/ui/DungeonRewardsDialog.tsx:3:24 - error TS2307: Cannot find module '@/components/ui/button' or its corresponding type declarations.

3 import { Button } from '@/components/ui/button';
                         ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/game/ui/DungeonRewardsDialog.tsx:41:42 - error TS7006: Parameter 'open' implicitly has an 'any' type.

41     <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
                                            ~~~~

client/src/game/ui/PetSpecterUI.tsx:7:31 - error TS2307: Cannot find module '@/components/TrainingFeeDialog' or its corresponding type declarations.

7 import TrainingFeeDialog from '@/components/TrainingFeeDialog';
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

client/src/game/ui/PetSpecterUI.tsx:8:25 - error TS2307: Cannot find module '@/contexts/Web3Context' or its corresponding type declarations.

8 import { useWeb3 } from '@/contexts/Web3Context';
                          ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/game/ui/PetSpecterUI.tsx:9:28 - error TS2307: Cannot find module '@/services/petService' or its corresponding type declarations.

9 import { PetService } from '@/services/petService';
                             ~~~~~~~~~~~~~~~~~~~~~~~

client/src/hooks/use-toast.ts:6:8 - error TS2307: Cannot find module '@/components/ui/toast' or its corresponding type declarations.

6 } from "@/components/ui/toast"
         ~~~~~~~~~~~~~~~~~~~~~~~

client/src/hooks/use-toast.ts:158:22 - error TS7006: Parameter 'open' implicitly has an 'any' type.

158       onOpenChange: (open) => {
                         ~~~~

client/src/hooks/useMultiplayer.ts:2:87 - error TS2307: Cannot find module '@shared/schema' or its corresponding type declarations.

2 import { NetworkMessage, MessageType, PlayerState, GameState, WeaponEffectData } from '@shared/schema';
                                                                                        ~~~~~~~~~~~~~~~~

client/src/hooks/useTournamentBattleConnection.ts:2:45 - error TS2307: Cannot find module '@shared/schema' or its corresponding type declarations.

2 import { MessageType, NetworkMessage } from '@shared/schema';
                                              ~~~~~~~~~~~~~~~~

client/src/hooks/useTournamentBattleConnection.ts:3:85 - error TS2307: Cannot find module '@/utils/webSocketUtils' or its corresponding type declarations.

3 import { createWebSocketConnection, WebSocketConnectionType, getWebSocketUrl } from '@/utils/webSocketUtils';
                                                                                      ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/hooks/useTournamentBattleConnection.ts:4:124 - error TS2307: Cannot find module '@/utils/unifiedWebSocket' or its corresponding type declarations.

4 import { createTournamentBattleConnection, joinTournamentBattle as joinBattle, leaveTournamentBattle as leaveBattle } from '@/utils/unifiedWebSocket';
                                                                                                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~

client/src/hooks/useTournamentBattleConnection.ts:291:21 - error TS7006: Parameter 'error' implicitly has an 'any' type.

291       ws.onerror = (error) => {
                        ~~~~~

client/src/hooks/useTournamentBattleConnection.ts:320:21 - error TS7006: Parameter 'event' implicitly has an 'any' type.

320       ws.onclose = (event) => {
                        ~~~~~

client/src/pages/AuthCallback.tsx:3:37 - error TS2307: Cannot find module '@/components/MinimalLoadingIndicator' or its corresponding type declarations.

3 import MinimalLoadingIndicator from '@/components/MinimalLoadingIndicator';
                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

client/src/pages/Game.tsx:2:37 - error TS2307: Cannot find module '@/components/MinimalLoadingIndicator' or its corresponding type declarations.

2 import MinimalLoadingIndicator from '@/components/MinimalLoadingIndicator';
                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

client/src/pages/Game.tsx:4:24 - error TS2307: Cannot find module '@/components/ui/button' or its corresponding type declarations.

4 import { Button } from '@/components/ui/button';
                         ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/pages/Game.tsx:5:99 - error TS2307: Cannot find module '@/components/ui/dialog' or its corresponding type declarations.

5 import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
                                                                                                    ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/pages/Game.tsx:7:26 - error TS2307: Cannot find module '@/hooks/use-toast' or its corresponding type declarations.

7 import { useToast } from '@/hooks/use-toast';
                           ~~~~~~~~~~~~~~~~~~~

client/src/pages/Game.tsx:8:25 - error TS2307: Cannot find module '@/contexts/AuthContext' or its corresponding type declarations.

8 import { useAuth } from '@/contexts/AuthContext';
                          ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/pages/Game.tsx:11:32 - error TS2307: Cannot find module '@/hooks/useMultiplayer' or its corresponding type declarations.

11 import { useMultiplayer } from '@/hooks/useMultiplayer';
                                  ~~~~~~~~~~~~~~~~~~~~~~~~

client/src/pages/Game.tsx:12:39 - error TS2307: Cannot find module '@shared/schema' or its corresponding type declarations.

12 import { GameMode, PlayerState } from '@shared/schema';
                                         ~~~~~~~~~~~~~~~~

client/src/pages/Game.tsx:13:29 - error TS2307: Cannot find module '@/hooks/use-mobile' or its corresponding type declarations.

13 import { useIsMobile } from '@/hooks/use-mobile';
                               ~~~~~~~~~~~~~~~~~~~~

client/src/pages/Game.tsx:14:35 - error TS2307: Cannot find module '@/components/NFTMintDialogProvider' or its corresponding type declarations.

14 import NFTMintDialogProvider from '@/components/NFTMintDialogProvider';
                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

client/src/pages/Game.tsx:15:39 - error TS2307: Cannot find module '@/components/NFTBasedPetDialogProvider' or its corresponding type declarations.

15 import NFTBasedPetDialogProvider from '@/components/NFTBasedPetDialogProvider';
                                         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

client/src/pages/Game.tsx:16:43 - error TS2307: Cannot find module '@/components/AIPetGenerationDialogProvider' or its corresponding type declarations.

16 import AIPetGenerationDialogProvider from '@/components/AIPetGenerationDialogProvider';
                                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

client/src/pages/Game.tsx:17:38 - error TS2307: Cannot find module '@/components/NFTBrowserDialogProvider' or its corresponding type declarations.

17 import NFTBrowserDialogProvider from '@/components/NFTBrowserDialogProvider';
                                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

client/src/pages/Game.tsx:147:17 - error TS7006: Parameter 'connectionId' implicitly has an 'any' type.

147     onConnect: (connectionId) => {
                    ~~~~~~~~~~~~

client/src/pages/Game.tsx:165:20 - error TS7006: Parameter 'data' implicitly has an 'any' type.

165     onPlayerJoin: (data) => {
                       ~~~~

client/src/pages/Game.tsx:190:21 - error TS7006: Parameter 'data' implicitly has an 'any' type.

190     onPlayerLeave: (data) => {
                        ~~~~

client/src/pages/Game.tsx:208:22 - error TS7006: Parameter 'player' implicitly has an 'any' type.

208     onPlayerUpdate: (player) => {
                         ~~~~~~

client/src/pages/Game.tsx:325:25 - error TS7006: Parameter 'data' implicitly has an 'any' type.

325     onSpecterCaptured: (data) => {
                            ~~~~

client/src/pages/Game.tsx:356:23 - error TS7006: Parameter 'data' implicitly has an 'any' type.

356     onLevelComplete: (data) => {
                          ~~~~

client/src/pages/Game.tsx:370:21 - error TS7006: Parameter 'data' implicitly has an 'any' type.

370     onChatMessage: (data) => {
                        ~~~~

client/src/pages/Game.tsx:374:15 - error TS7006: Parameter 'error' implicitly has an 'any' type.

374     onError: (error) => {
                  ~~~~~

client/src/pages/Game.tsx:384:29 - error TS7006: Parameter 'effectData' implicitly has an 'any' type.

384     onWeaponEffectCreated: (effectData) => {
                                ~~~~~~~~~~

client/src/pages/Game.tsx:389:29 - error TS7006: Parameter 'effectId' implicitly has an 'any' type.

389     onWeaponEffectRemoved: (effectId) => {
                                ~~~~~~~~

client/src/pages/Game.tsx:1284:80 - error TS7006: Parameter 'open' implicitly has an 'any' type.

1284       <Dialog open={isPaused && !showGameOver && !isDialogOpen} onOpenChange={(open) => {
                                                                                    ~~~~

client/src/pages/Game.tsx:1306:50 - error TS7006: Parameter 'open' implicitly has an 'any' type.

1306       <Dialog open={showGameOver} onOpenChange={(open) => {
                                                      ~~~~

client/src/pages/Home.tsx:35:39 - error TS2307: Cannot find module '@shared/schema' or its corresponding type declarations.

35 import { GameMode, MessageType } from '@shared/schema';
                                         ~~~~~~~~~~~~~~~~

client/src/services/aiGenerationService.ts:147:13 - error TS2322: Type 'unknown' is not assignable to type 'string | undefined'.

147             requestId: analysisResult.requestId,
                ~~~~~~~~~

  client/src/services/aiGenerationService.ts:124:5
    124     requestId?: string;
            ~~~~~~~~~
    The expected type comes from property 'requestId' which is declared here on type '{ imageUrl: string; analysis?: { recommendedType: string; colorPalette: string[]; description: string; style: string; } | undefined; requestId?: string | undefined; queuePosition?: number | undefined; }'

client/src/services/aiGenerationService.ts:148:13 - error TS2322: Type 'unknown' is not assignable to type 'number | undefined'.

148             queuePosition: analysisResult.queuePosition
                ~~~~~~~~~~~~~

  client/src/services/aiGenerationService.ts:125:5
    125     queuePosition?: number;
            ~~~~~~~~~~~~~
    The expected type comes from property 'queuePosition' which is declared here on type '{ imageUrl: string; analysis?: { recommendedType: string; colorPalette: string[]; description: string; style: string; } | undefined; requestId?: string | undefined; queuePosition?: number | undefined; }'

client/src/utils/disableHmr.ts:6:33 - error TS2835: Relative import paths need explicit file extensions in ECMAScript imports when '--moduleResolution' is 'node16' or 'nodenext'. Did you mean './unifiedWebSocket.js'?

6 import { NativeWebSocket } from './unifiedWebSocket';
                                  ~~~~~~~~~~~~~~~~~~~~

client/src/utils/webSocketUtils.ts:6:46 - error TS2835: Relative import paths need explicit file extensions in ECMAScript imports when '--moduleResolution' is 'node16' or 'nodenext'. Did you mean './unifiedWebSocket.js'?

6 import { createWebSocket, sendMessage } from './unifiedWebSocket';
                                               ~~~~~~~~~~~~~~~~~~~~

server/memPetStorage.ts:8:8 - error TS2307: Cannot find module '@shared/petSchema' or its corresponding type declarations.

8 } from "@shared/petSchema";
         ~~~~~~~~~~~~~~~~~~~

server/postgresStorage.ts:57:58 - error TS2322: Type '"single"' is not assignable to type 'GameMode'.

57   async saveHighScore(playerName: string, score: number, gameMode: GameMode = 'single', teamName?: string): Promise<HighScore> {
                                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

shared/petSchema.ts:4:23 - error TS2835: Relative import paths need explicit file extensions in ECMAScript imports when '--moduleResolution' is 'node16' or 'nodenext'. Did you mean './schema.js'?

4 import { users } from "./schema";
                        ~~~~~~~~~~


Found 379 errors in 86 files.

Errors  Files
    50  client/src/__tests__/game/controls/MobileControls.test.tsx:8
    20  client/src/__tests__/hooks/use-mobile.test.tsx:4
    74  client/src/__tests__/hooks/useMultiplayer.test.tsx:7
    14  client/src/components/AIPetGenerationDialog.tsx:9
     1  client/src/components/AuthProtection.tsx:3
     4  client/src/components/DualLoginButton.tsx:2
     6  client/src/components/NFTBasedPetDialog.tsx:9
     8  client/src/components/NFTBrowser.tsx:2
     8  client/src/components/NFTMintDialog.tsx:9
     1  client/src/components/NFTMintDialogProvider.tsx:3
     1  client/src/components/PVPArenaDialog.tsx:117
     4  client/src/components/TournamentBattleDialog.tsx:7
     4  client/src/components/TournamentDialog.tsx:9
     5  client/src/components/TrainingFeeDialog.tsx:9
     3  client/src/components/WalletConnectButton.tsx:2
     1  client/src/components/ui/accordion.tsx:5
     2  client/src/components/ui/alert-dialog.tsx:4
     1  client/src/components/ui/alert.tsx:4
     1  client/src/components/ui/avatar.tsx:4
     1  client/src/components/ui/badge.tsx:4
     1  client/src/components/ui/breadcrumb.tsx:5
     1  client/src/components/ui/button.tsx:5
     2  client/src/components/ui/calendar.tsx:5
     1  client/src/components/ui/card.tsx:3
     2  client/src/components/ui/carousel.tsx:7
     1  client/src/components/ui/chart.tsx:4
     1  client/src/components/ui/checkbox.tsx:5
     2  client/src/components/ui/command.tsx:6
     1  client/src/components/ui/context-menu.tsx:5
     1  client/src/components/ui/dialog.tsx:5
     1  client/src/components/ui/drawer.tsx:4
     1  client/src/components/ui/dropdown-menu.tsx:5
     2  client/src/components/ui/form.tsx:13
     1  client/src/components/ui/hover-card.tsx:4
     1  client/src/components/ui/input-otp.tsx:5
     1  client/src/components/ui/input.tsx:3
     1  client/src/components/ui/label.tsx:5
     1  client/src/components/ui/menubar.tsx:5
     1  client/src/components/ui/navigation-menu.tsx:6
     4  client/src/components/ui/pagination.tsx:4
     1  client/src/components/ui/popover.tsx:4
     1  client/src/components/ui/progress.tsx:4
     1  client/src/components/ui/radio-group.tsx:5
     1  client/src/components/ui/resizable.tsx:4
     1  client/src/components/ui/scroll-area.tsx:4
     1  client/src/components/ui/select.tsx:5
     1  client/src/components/ui/separator.tsx:4
     1  client/src/components/ui/sheet.tsx:6
     9  client/src/components/ui/sidebar.tsx:6
     1  client/src/components/ui/skeleton.tsx:1
     1  client/src/components/ui/slider.tsx:4
     1  client/src/components/ui/switch.tsx:4
     1  client/src/components/ui/table.tsx:3
     1  client/src/components/ui/tabs.tsx:4
     1  client/src/components/ui/textarea.tsx:3
     1  client/src/components/ui/toast.tsx:6
     6  client/src/components/ui/toaster.tsx:1
     4  client/src/components/ui/toggle-group.tsx:5
     1  client/src/components/ui/toggle.tsx:5
     1  client/src/components/ui/tooltip.tsx:4
     1  client/src/contexts/AuthContext.tsx:2
     1  client/src/contexts/PvpAccessContext.tsx:2
     2  client/src/contexts/Web3Context.tsx:6
    15  client/src/game/battle/BattleManager.ts:87
     1  client/src/game/battle/TestTournamentBattle.ts:1
    26  client/src/game/battle/TournamentBattleRenderer.ts:3
     2  client/src/game/effects/GravityEffect.ts:121
     1  client/src/game/effects/ParticleSystem.ts:620
     1  client/src/game/engine/InputHandler.ts:3
     8  client/src/game/entities/DungeonBoss.ts:3
     1  client/src/game/entities/Specter.ts:16
     1  client/src/game/pvp/PVPArenaManager.ts:5
     3  client/src/game/ui/DungeonRewardsDialog.tsx:2
     3  client/src/game/ui/PetSpecterUI.tsx:7
     2  client/src/hooks/use-toast.ts:6
     1  client/src/hooks/useMultiplayer.ts:2
     5  client/src/hooks/useTournamentBattleConnection.ts:2
     1  client/src/pages/AuthCallback.tsx:3
    24  client/src/pages/Game.tsx:2
     1  client/src/pages/Home.tsx:35
     2  client/src/services/aiGenerationService.ts:147
     1  client/src/utils/disableHmr.ts:6
     1  client/src/utils/webSocketUtils.ts:6
     1  server/memPetStorage.ts:8
     1  server/postgresStorage.ts:57
     1  shared/petSchema.ts